import type { Env } from "@/api/types";
import { getSupabaseClient } from "@/api/utils";
import { createUnauthorizedResponse } from "@/api/utils/auth";
import type { Context } from "hono";

type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

export async function handleGetWebsite(c: Context<{ Bindings: Env; Variables: HandlerVariables }>) {
  try {
    // Get auth from context (set by middleware)
    const auth = c.get("auth");

    if (!auth?.isAuthenticated || !auth.userId) {
      return createUnauthorizedResponse("Authentication required");
    }

    // Get the website ID from the URL params
    const { websiteId } = c.req.param();

    if (!websiteId) {
      return c.json({ error: "Website ID is required" }, 400);
    }

    // Get JWT token from request for user-specific operations
    const authHeader = c.req.header("Authorization");
    const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : undefined;

    // Initialize Supabase client
    const { client: supabaseClient, error: supabaseError } = getSupabaseClient(c.env, jwt);
    if (supabaseError || !supabaseClient) {
      console.error("Supabase client initialization error:", supabaseError);
      return c.json({
        error: "Server configuration error: Unable to connect to database",
        details: "Failed to initialize Supabase client"
      }, 500);
    }

    // Query the website with its theme
    // Use the specific relationship to avoid ambiguity
    const { data: website, error: websiteError } = await supabaseClient
      .from('websites')
      .select('*, theme:website_themes!websites_theme_id_fkey(*)')
      .eq('id', websiteId)
      .single();

    if (websiteError) {
      console.error("Error fetching website:", websiteError);
      return c.json({
        error: "Failed to fetch website",
        details: websiteError.message
      }, 500);
    }

    if (!website) {
      return c.json({ error: "Website not found" }, 404);
    }

    // If the website is published, anyone can access it
    // Otherwise, we'll rely on RLS policies to check team access
    // The fact that we were able to fetch the website means the user has access to it

    // Add cache headers for better performance
    // Published websites can be cached longer
    const maxAge = website.published ? 300 : 30; // 5 minutes for published, 30 seconds for drafts
    c.header('Cache-Control', `public, max-age=${maxAge}, stale-while-revalidate=300`);

    return c.json({
      success: true,
      website
    });
  } catch (error) {
    console.error("Error fetching website:", error);
    return c.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error occurred"
    }, 500);
  }
}
