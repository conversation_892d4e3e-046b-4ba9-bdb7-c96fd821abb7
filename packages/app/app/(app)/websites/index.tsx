import AddIcon from "@/assets/icons/add.svg";
import WebDesignBulkIcon from "@/assets/icons/bulk/web-design-01.svg";
import EditIcon from "@/assets/icons/edit-02.svg";
import WebsiteIcon from "@/assets/icons/globe-02.svg";
import WebsiteIllustration from "@/assets/illustrations/storyset/Website designer-rafiki.svg";
import { EmptyPagePlaceholder } from "@/components/EmptyPagePlaceholder";
import Modal, { ModalFooter, ModalHeader } from "@/components/Modal"; // Added ModalHeader, ModalFooter
import PageLayout, { PageRow } from "@/components/PageLayout";
import { Badge, Box, Button, Center, Heading, Input, Label, Text } from "@/components/ui";
import { useCurrentTeamId } from "@/hooks/useCurrentTeamId";
import { useWebsitesApi } from "@/hooks/useWebsitesApi";
import type { Website } from "@/types/websites";
import { cx, getSubdomainUrl } from "@/utils";
import { getWebsiteHostname } from "@/utils/environment";
import { useRouter } from "expo-router";
import { useCallback, useEffect, useState } from "react";
import { ActivityIndicator, Pressable } from "react-native";
import UserInterfaceIllustration from '@platform/assets/illustrations/user-interface.svg'

export default function WebsitesList() {
  const [teamAccountId] = useCurrentTeamId();
  const { useTeamWebsites, createWebsite, isCreatingWebsite } = useWebsitesApi();
  const router = useRouter();
  const [websites, setWebsites] = useState<Website[]>([]);

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const hostname = getWebsiteHostname();

  // Form state for creating a new website
  const [title, setTitle] = useState("");
  const [subdomain, setSubdomain] = useState("");

  const [error, setError] = useState<string | null>(null);

  // Function to apply mask for subdomain (only lowercase alphanumeric and dashes)
  const applySubdomainMask = (text: string) => {
    // Convert to lowercase and replace any character that is not alphanumeric or dash
    return text.toLowerCase().replace(/[^a-z0-9-]/g, "");
  };

  const openCreateModal = () => {
    setIsCreateModalOpen(true);
    // Reset form values
    setTitle("");
    setSubdomain("");
    setError(null);
  };

  const closeCreateModal = () => {
    setIsCreateModalOpen(false);
  };

  const handleCreateWebsite = async () => {
    if (!teamAccountId) return;

    setError(null);
    try {
      const response = await createWebsite({
        team_account_id: teamAccountId as string,
        title,
        subdomain,
        published: false,
        status: "draft",
        theme: {
          primary_color: "#3B82F6",
          secondary_color: "#10B981",
          font_family: "Inter",
        },
      });
      console.log("Website created successfully:", response);
      closeCreateModal();
      refetch(); // Reload websites after creation
    } catch (err) {
      console.error("Error creating website:", err);

      // Handle specific error cases
      if (err instanceof Error) {
        if (err.message.includes("Subdomain already exists")) {
          setError("Este subdomínio já está em uso. Por favor, escolha outro subdomínio.");
        } else {
          setError(err.message);
        }
      } else {
        setError("Falha ao criar o website");
      }
    }
  };

  // Use the hook to fetch websites with optimized settings
  const {
    data: websitesData,
    isLoading,
    refetch,
    isFetching,
  } = useTeamWebsites({
    // Only enable if we have a team ID
    enabled: !!teamAccountId,
  });

  // Set websites from the hook data
  useEffect(() => {
    if (websitesData) {
      setWebsites(websitesData);
    }
  }, [websitesData]);

  // Show loading state only on initial load, not on refetches
  if ((isLoading || (isFetching && websites.length === 0)) && !websitesData) {
    return (
      <Box className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" />
      </Box>
    );
  }

  return (
    <PageLayout
      pageTitle="Websites"
      description="Gerencie os websites da sua equipe"
      pageIcon={WebsiteIcon}
      actions={[
        <Button size="sm" key="add-website-header-action" onPress={openCreateModal}>
          <AddIcon className="mr-2 w-[16px] text-text-inverse" />
          <Text>Criar site</Text>
        </Button>,
      ]}
    >
      {websites.length > 0 ? (
        <PageRow className="flex-col">
          {websites.map((website) => (
            <Pressable key={website.id} onPress={() => router.push(`/websites/${website.id}`)}>
              <Box className="flex-row items-center justify-between rounded border border-border-light p-6 shadow-sm">
                <Box className="flex-row items-center">
                  <Center className="mr-6 h-12 w-12 rounded-lg bg-background-darker shadow-xs">
                    <WebDesignBulkIcon className="h-6 w-6 text-gray-500" />
                  </Center>
                  <Box>
                    <Text className={cx("font-medium", { "opacity-30": !website.title })}>
                      {website.title || "Sem título"}
                    </Text>
                    <Text className="text-sm text-text-secondary">
                      {getSubdomainUrl(website.subdomain).replace("https://", "")}
                    </Text>
                  </Box>
                  <Badge className="ml-6 w-fit" size="xs" variant={website.published ? "primary-outline" : "outline"}>
                    <Text>{website.published ? "Publicado" : "Rascunho"}</Text>
                  </Badge>
                </Box>
                <EditIcon className="h-5 w-5 text-gray-500" />
              </Box>
            </Pressable>
          ))}
        </PageRow>
      ) : (
        <EmptyPagePlaceholder
          illustration={UserInterfaceIllustration}
          title="Configure seu website"
          description="Esta organização ainda não tem um website. Clique no botão abaixo para configurar seu website, leva menos de 5 minutos!"
          button={{
            label: "Configurar website",
            icon: "navigate",
            position: "after",
            onPress: openCreateModal,
          }}
        />
      )}

      {/* Create Website Modal */}
      <Modal isOpen={isCreateModalOpen} onClose={closeCreateModal}>
        <ModalHeader title="Criar novo site" onClose={closeCreateModal} icon={WebsiteIcon} />
        {/* Main Content */}
        <Box className="p-6">
          <Box className="space-y-4">
            <Box className="mb-4">
              <Label htmlFor="subdomain">Subdomínio</Label>
              <Input
                autoFocus
                value={subdomain}
                onChangeText={(text) => setSubdomain(applySubdomainMask(text))}
                placeholder="seu-subdominio"
                size="md"
                fullWidth
                editable={!isCreatingWebsite}
                className="mt-1"
                autoCapitalize="none"
                autoCorrect={false}
              />
              <Text className="mt-2 text-sm text-text-secondary">
                Seu site estará disponível em{" "}
                <Text className="font-medium text-primary text-sm">
                  {getSubdomainUrl(subdomain || "seu-subdominio").replace("https://", "")}
                </Text>
              </Text>

              <Box className="mt-4 gap-1.5 rounded-md border border-primary-100 bg-primary-50 p-4 shadow-xs">
                <Text className="text-text-secondary text-xs">
                  • Seu domínio deve conter pelo menos 5 caracteres (letras, números e traços)
                </Text>
                <Text className="text-text-secondary text-xs">
                  • Você pode alterar o subdomínio a qualquer momento.
                </Text>
                <Text className="text-text-secondary text-xs">
                  • Após criar um site, você pode{" "}
                  <Text className="font-medium text-xs">adicionar um domínio personalizado</Text> nas{" "}
                  <Text className="font-medium text-xs">configurações do site</Text>.
                </Text>
              </Box>
            </Box>

            {error && <Text className="mb-4 text-error">{error}</Text>}
          </Box>
        </Box>
        {/* Footer */}
        <ModalFooter
          secondaryAction={{
            label: "Cancelar",
            onPress: closeCreateModal,
            disabled: isCreatingWebsite,
            variant: "destructive-link", // Explicitly set variant if needed, default might be different
          }}
          primaryAction={{
            label: "Criar site",
            onPress: handleCreateWebsite,
            disabled: !subdomain || subdomain.length < 5 || isCreatingWebsite,
            // isLoading prop is not directly supported by ModalFooterActionProps,
            // but the disabled state covers the isSubmitting case.
            // If visual loading state is needed, ModalFooter might need adjustment or use children.
          }}
        />
      </Modal>
    </PageLayout>
  );
}
