import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import <PERSON>tieView from "lottie-react-native";
import { Center, Form, FormField, FormInput, Heading, HStack, Icon, LabelSeparator } from "@/components/ui";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useForm } from "react-hook-form";
import { Link, useRouter, useLocalSearchParams } from "expo-router";
import { Platform } from "react-native";
import ImoblrSymbol from "@/assets/logos/imoblr-symbol.svg";
import GoogleLogo from "@/assets/logos/google-logo.svg";
import AppleLogo from "@/assets/logos/apple-logo.svg";
import DashboardSettingIcon from "@/assets/icons/dashboard-setting.svg";
import Reanimated, { FadeIn, FadeOut } from "react-native-reanimated";
import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";
import OTPComponent from "@/components/OTP";
import MailSentIllustration from "@/assets/illustrations/undraw/undraw_mail-sent_ujev.svg";

const formSchema = z.object({
  email: z.string().email({
    message: "O endereço de email precisa ser válido.",
  }),
  otp: z
    .string()
    .length(6, {
      message: "O código de verificação precisa ter 6 dígitos.",
    })
    .optional(),
});

type FormWithInputs = z.infer<typeof formSchema>;
type FieldName = keyof FormWithInputs;

const signUpSteps = [
  {
    fields: ["email"],
  },
  {
    fields: ["otp"],
  },
];

export default function Screen() {
  const [signUpStep, setSignUpStep] = React.useState(1);
  const [isSigningUp, setIsSigningUp] = React.useState(false);
  const [isSendingLink, setIsSendingLink] = React.useState(false);
  const [isVerifyingOtp, setIsVerifyingOtp] = React.useState(false);
  const [userEmail, setUserEmail] = React.useState("");
  const [isRedirecting, setIsRedirecting] = React.useState(false);
  const [preventRedirect, setPreventRedirect] = React.useState(false);
  const { returnTo, email: emailFromParams } = useLocalSearchParams<{ returnTo?: string, email?: string }>();
  const [otpError, setOtpError] = React.useState<string | null>(null);
  const animationRef = React.useRef<LottieView>(null);
  const { session, sendMagicLink, verifyOtp, loading } = useSupabaseAuth();
  const router = useRouter();
  const form = useForm<FormWithInputs>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      otp: "",
    },
  });

  const [countdown, setCountdown] = React.useState(0);

  React.useEffect(() => {
    if (emailFromParams) {
      form.setValue("email", emailFromParams);
      sendVerificationEmail();
    }
  }, [emailFromParams]);

  React.useEffect(() => {
    let timer: NodeJS.Timeout | undefined;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [countdown]);

  const handleResendCode = () => {
    sendVerificationEmail();
    setCountdown(60); // Start countdown when the email is sent
  };

  React.useEffect(() => {
    if (signUpStep === 3) {
      setTimeout(() => {
        animationRef.current?.play();
        setTimeout(() => {
          animationRef.current?.pause();
        }, 4000);
      }, 750);
    }

    // Clear OTP value when returning to step 1
    if (signUpStep === 1) {
      form.setValue("otp", "");
    }
  }, [signUpStep, form]);

  // Handle automatic redirect for logged-in users
  React.useEffect(() => {
    if (!loading && session && !preventRedirect) {
      // Check if we have a returnTo path (for invitation flow)
      if (returnTo) {
        router.replace(returnTo as any);
      } else {
        // Check for pending invitation in localStorage (web only)
        if (Platform.OS === "web" && typeof window !== "undefined") {
          const pendingInviteToken = localStorage.getItem("pendingInviteToken");
          if (pendingInviteToken) {
            // Clear the stored token
            localStorage.removeItem("pendingInviteToken");
            // Redirect to invitation acceptance page
            router.replace(`/aceitar-convite?token=${pendingInviteToken}`);
            return;
          }
        }
        // Default redirect to home
        router.replace("/");
      }
    }
  }, [loading, session, router, preventRedirect, returnTo]);

  const sendVerificationEmail = async () => {
    const fields = signUpSteps[0].fields;
    const output = await form.trigger(fields as FieldName[], {
      shouldFocus: true,
    });

    if (!output) return;

    const email = form.getValues("email");
    setIsSendingLink(true);
    setUserEmail(email);

    try {
      await sendMagicLink(email);
      setSignUpStep(2);
      setCountdown(60); // Start countdown when the email is sent
    } catch (error) {
      console.error("Failed to send verification email:", error);
    } finally {
      setIsSendingLink(false);
    }
  };

  async function verifyOtpCode(otp: string) {
    if (!userEmail || otp.length !== 6) return;

    setIsVerifyingOtp(true);
    setOtpError(null);

    try {
      await verifyOtp(userEmail, otp);
      setPreventRedirect(false); // Allow redirect to happen through the useEffect
      // We'll let the useEffect handle redirect logic now
    } catch (error) {
      console.error("OTP verification error:", error);
      setOtpError("Código de verificação inválido. Tente novamente.");
    } finally {
      setIsVerifyingOtp(false);
    }
  }

  const handleOtpChange = (value: string) => {
    form.setValue("otp", value);

    // Automatically verify when all 6 digits are entered
    if (value.length === 6) {
      verifyOtpCode(value);
    }
  };

  const handleViewDashboard = async () => {
    try {
      setIsRedirecting(true);
      router.replace("/");
    } catch (error) {
      console.error("Navigation error:", error);
      setIsRedirecting(false);
    }
  };

  return (
    <Center className="h-full w-full">
      {signUpStep === 1 && (
        <Reanimated.View
          entering={FadeIn.duration(300)}
          exiting={FadeOut.duration(300)}
          key="sign-up-step-1"
          style={{
            display: "flex",
            height: "100%",
            width: "100%",
            alignItems: "center",
            justifyContent: "center",
            position: "absolute",
          }}
        >
          <Center className="mb-8">
            <ImoblrSymbol className="mb-8 h-24 w-24" />
            <Heading size="3xl" className="mb-2 text-text">
              Acesse sua conta
            </Heading>
            <Text className="text-text-secondary">
              Não tem uma conta?{" "}
              <Link className="font-medium text-primary" href={{ pathname: "/cadastro" }}>
                Cadastre-se
              </Link>
            </Text>
          </Center>

          <Form {...form}>
            <Reanimated.View className="w-full max-w-[360px] gap-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormInput
                    raw
                    className="w-full"
                    autoFocus
                    label="Email"
                    placeholder="Digite seu email"
                    onKeyPress={(e) => {
                      if (e.nativeEvent.key === "Enter") {
                        sendVerificationEmail();
                      }
                    }}
                    {...field}
                  />
                )}
              />
              <Button
                className="w-full"
                size="lg"
                onPress={sendVerificationEmail}
                isLoading={isSendingLink}
                loadingMessage="Enviando código de verificação..."
              >
                <Text className="bg-brand">Entrar com email</Text>
              </Button>
              <LabelSeparator label="Ou cadastre-se com" />
              <HStack className="w-full">
                <Button variant="outline" className="flex-1">
                  <Icon as={GoogleLogo} />
                  <Text className="ml-4">Google</Text>
                </Button>
                <Button variant="outline" className="flex-1">
                  <Icon as={AppleLogo} className="h-[22px] w-[22px] text-[#000]" />

                  <Text className="ml-4">Apple</Text>
                </Button>
              </HStack>
              <Text className="px-8 text-center text-sm text-text-quaternary">
                Ao se cadastrar você concorda com os nossos &nbsp;
                <Link className="text-primary" href={{ pathname: "/termos-de-uso" }}>
                  Termos de Uso
                </Link>
                .
              </Text>
            </Reanimated.View>
          </Form>
        </Reanimated.View>
      )}
      {signUpStep === 2 && (
        <OTPComponent
          email={userEmail}
          otpValue={form.watch("otp") || ""}
          onOtpChange={handleOtpChange}
          onSubmit={verifyOtpCode}
          onCancel={() => {
            setOtpError(null);
            setSignUpStep(1);
          }}
          onResend={handleResendCode}
          error={otpError}
          onDismissError={() => {
            setOtpError(null);
          }}
          isVerifying={isVerifyingOtp}
          isResending={isSendingLink}
          countdown={countdown}
        />
      )}
      {signUpStep === 3 && (
        <Reanimated.View
          entering={FadeIn.duration(300).delay(300)}
          exiting={FadeOut.duration(300)}
          style={{
            display: "flex",
            height: "100%",
            width: "100%",
            alignItems: "center",
            justifyContent: "center",
            position: "absolute",
          }}
          key="sign-up-step-3"
        >
          <Center className="h-full w-full max-w-[360px]">
            <Reanimated.View className="mb-2 h-[60vw] w-[60vw] md:h-[25vh] md:w-[25vh]">
              <LottieView
                source={require("@/assets/animations/success-animation.json")}
                style={{ width: "100%", height: "100%" }}
                ref={animationRef}
                autoPlay={false}
                loop={false}
              />
            </Reanimated.View>

            <Text className="mb-2 font-medium font-title text-2xl text-gray-900">Sua conta foi criada!</Text>
            <Text className="text-center text-text-tertiary">
              Criamos sua conta e agora você pode visualizar seu painel. Clique no botão abaixo para continuar.
            </Text>
            <Button
              className="mt-8 w-full"
              size="lg"
              onPress={handleViewDashboard}
              isLoading={isRedirecting}
              loadingMessage="Entrando no painel..."
            >
              <Icon as={DashboardSettingIcon} className="mr-4 text-text-inverse" />
              <Text className="bg-brand">Visualizar meu painel</Text>
            </Button>
          </Center>
        </Reanimated.View>
      )}
    </Center>
  );
}
