import { useBillingStatus } from "@/hooks/useBillingStatus";
import { useDayjs } from "@/hooks/useDayjs";
import { Link } from "expo-router";
import React from "react";
import { CircularProgressBar } from "../CircularProgressBar";
import { Badge, Box, Text } from "../ui";

type TrialPeriodIndicatorProps = {
  isCollapsed?: boolean;
};

export const TrialPeriodIndicator = ({ isCollapsed = false }: TrialPeriodIndicatorProps) => {
  const { data: billingStatus, isLoading } = useBillingStatus();
  const dayjs = useDayjs();

  if (isLoading || !billingStatus) {
    return null;
  }

  // Only show for trialing or paused subscriptions
  if (!(billingStatus.status === "trialing" || billingStatus.status === "paused")) {
    return null;
  }

  const totalTrialDays = 7;
  const remainingDays = billingStatus?.trial_end
    ? Math.ceil(dayjs(billingStatus.trial_end).diff(dayjs(), "day", true))
    : 0;

  const isPaused = billingStatus.status === "paused";

  // For paused status, we still show the indicator even if trial has ended
  if (billingStatus.status === "trialing" && remainingDays <= 0) {
    return null;
  }

  return (
    <Link href="/configuracoes/faturamento" asChild>
      <Box
        className={`mx-3 mt-2 mb-4 cursor-pointer rounded-lg border ${isPaused ? "border-error-100 bg-error-50/10" : "border-primary-100 bg-background"} p-2 hover:bg-opacity-20`}
      >
        <Box className="flex-row items-center">
          {isPaused ? (
            <Badge variant="danger" size="xs" className="mr-2">
              <Text>PAUSADO</Text>
            </Badge>
          ) : (
            <CircularProgressBar
              progress={remainingDays / totalTrialDays}
              size={24}
              label={`${remainingDays}`}
              strokeWidth={2}
            />
          )}
          {!isCollapsed && (
            <Box className="ml-2 flex-1">
              {isPaused ? (
                <Text className="font-medium text-text text-xs">
                  Assinatura pausada:
                  <Text className="text-2xs text-text-secondary">&nbsp; Clique aqui para selecionar um plano</Text>
                </Text>
              ) : (
                <>
                  <Text className="font-medium text-text text-xs">{remainingDays} dias restantes</Text>
                  <Text className="text-2xs text-text-secondary">Período de teste</Text>
                </>
              )}
            </Box>
          )}
        </Box>
      </Box>
    </Link>
  );
};

export default TrialPeriodIndicator;
